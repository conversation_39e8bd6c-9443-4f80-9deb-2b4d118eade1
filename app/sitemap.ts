import { MetadataRoute } from 'next'
import { client } from '@/sanity/client'
import { SanityDocument } from 'next-sanity'

const PROJECTS_QUERY = `*[_type == "project" && defined(slug.current)] {
  slug,
  _updatedAt
}`

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl =
    process.env.NEXT_PUBLIC_SITE_URL || 'https://sagebrushcreative.com'

  // Get all projects from Sanity
  const projects = await client.fetch<SanityDocument[]>(PROJECTS_QUERY)

  // Static pages
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 1,
    },
    {
      url: `${baseUrl}/work`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
  ]

  // Dynamic project pages
  const projectPages: MetadataRoute.Sitemap = projects.map(project => ({
    url: `${baseUrl}/project/${project.slug.current}`,
    lastModified: new Date(project._updatedAt),
    changeFrequency: 'monthly' as const,
    priority: 0.6,
  }))

  return [...staticPages, ...projectPages]
}
