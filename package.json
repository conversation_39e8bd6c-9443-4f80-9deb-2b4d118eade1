{"name": "hannah-portfolio", "version": "0.1.0", "license": "MIT", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write ."}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@portabletext/react": "^4.0.3", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "1.1.1", "@sanity/image-url": "^1.2.0", "@types/styled-components": "^5.1.34", "@vercel/analytics": "^1.5.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.15.0", "lucide-react": "^0.454.0", "next": "15.2.4", "next-sanity": "^9.12.3", "next-themes": "latest", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-is": "^19.1.1", "resend": "^4.5.1", "simple-icons": "^14.15.0", "sonner": "^2.0.3", "styled-components": "^6.1.19", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^9.27.0", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5"}}